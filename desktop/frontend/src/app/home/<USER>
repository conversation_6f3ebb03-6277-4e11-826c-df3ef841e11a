<!-- Dashboard Layout -->
@if (tabService.tabsValue.length === 0) {
<div class="p-6 max-w-4xl mx-auto space-y-6">
  <!-- Welcome State -->
  <div class="card">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        Welcome to NS Drive Dashboard
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Start by creating your first sync operation
      </p>
    </div>
    <div class="mb-6">
      <p class="text-gray-700 dark:text-gray-300 mb-6">
        Sync operations allow you to synchronize files between local directories
        and cloud storage services. Each operation runs independently with its
        own configuration and profile.
      </p>
      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <lucide-icon
            [img]="RefreshCwIcon"
            class="w-6 h-6 text-primary-600"
          ></lucide-icon>
          <span class="text-gray-700 dark:text-gray-300"
            >Real-time synchronization</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <lucide-icon
            [img]="CloudIcon"
            class="w-6 h-6 text-primary-600"
          ></lucide-icon>
          <span class="text-gray-700 dark:text-gray-300"
            >Multiple cloud providers</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <lucide-icon
            [img]="SettingsIcon"
            class="w-6 h-6 text-primary-600"
          ></lucide-icon>
          <span class="text-gray-700 dark:text-gray-300"
            >Customizable profiles</span
          >
        </div>
      </div>
    </div>
    <div class="flex justify-end">
      <button class="btn-primary whitespace-nowrap" (click)="createTab()">
        <div class="flex items-center space-x-2">
          <lucide-icon [img]="PlusIcon" class="w-5 h-5 mr-2"></lucide-icon>
          Create First Operation
        </div>
      </button>
    </div>
  </div>
</div>
}

<!-- Operations Dashboard -->
@if (tabService.tabsValue.length > 0) {
<div class="flex flex-col h-full">
  <!-- Fixed Header and Tab Navigation -->
  <div
    class="fixed top-0 left-0 right-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
  >
    <div class="max-w-6xl mx-auto">
      <div class="p-6">
        <div class="mb-6">
          <div class="flex items-center space-x-3 mb-2">
            <lucide-icon
              [img]="RefreshCwIcon"
              class="w-6 h-6 text-primary-600"
            ></lucide-icon>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Sync Operations
            </h1>
          </div>
          <p class="text-gray-600 dark:text-gray-400">
            {{ tabService.tabsValue.length }} operation(s) configured
          </p>
        </div>
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <nav class="flex space-x-8 overflow-x-auto hide-scrollbar">
              @for ( tab of tabService.tabsValue; track trackByTabId(i, tab);
              let i = $index) {
              <button
                [class]="
                  getActiveTabIndex() === i
                    ? 'tab-button-active whitespace-nowrap'
                    : 'tab-button whitespace-nowrap'
                "
                (click)="onTabChange(i)"
              >
                <div class="flex items-center space-x-2">
                  <span class="whitespace-nowrap">{{
                    tab?.name || "Operation " + (i + 1)
                  }}</span>
                  @if (tab && tab.id) {
                  <div class="flex items-center space-x-1 ml-2">
                    <!-- Rename Button -->
                    <button
                      class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 whitespace-nowrap"
                      (click)="$event.stopPropagation(); startRenameTab(tab.id)"
                      title="Rename tab"
                    >
                      <lucide-icon
                        [img]="EditIcon"
                        class="w-4 h-4"
                      ></lucide-icon>
                    </button>
                    <!-- Delete Button -->
                    <button
                      class="p-1 rounded hover:bg-red-100 dark:hover:bg-red-900 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 whitespace-nowrap"
                      (click)="$event.stopPropagation(); deleteTab(tab.id)"
                      title="Delete tab"
                    >
                      <lucide-icon
                        [img]="Trash2Icon"
                        class="w-4 h-4"
                      ></lucide-icon>
                    </button>
                  </div>
                  }
                </div>
              </button>
              }
            </nav>
            <!-- Add New Operation Button -->
            <button class="btn-primary" (click)="createTab()">
              <lucide-icon [img]="PlusIcon" class="w-5 h-5"></lucide-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scrollable Tab Content -->
  <div class="flex-1 overflow-auto fixed-header-offset">
    <div class="p-6 max-w-6xl mx-auto">
      <!-- Tab Content -->
      @for ( tab of tabService.tabsValue; track trackByTabId(i, tab); let i =
      $index) {
      <div>
        @if (getActiveTabIndex() === i) {
        <div class="space-y-6">
          <!-- Profile Selection -->
          <div>
            <label
              for="profile-select-{{ i }}"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Sync Profile
            </label>
            <div class="relative">
              <select
                id="profile-select-{{ tab?.id }}"
                class="select-field pr-10"
                [value]="getTabProfileValue(tab)"
                (change)="onProfileChange($event, tab?.id)"
                [disabled]="!tab || !tab.id"
                #profileSelect
              >
                <option [value]="null">No profile selected</option>
                @for ( profile of appService.configInfo$.value.profiles; track
                profile; let idx = $index) {
                <option
                  [value]="idx"
                  [selected]="tab?.selectedProfileIndex === idx"
                >
                  {{ profile.name }}
                </option>
                }
              </select>
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <lucide-icon
                  [img]="ChevronDownIcon"
                  class="w-5 h-5 text-gray-400"
                ></lucide-icon>
              </div>
            </div>
          </div>
          <!-- Working Directory Section -->
          <div>
            <div class="flex items-center space-x-2 mb-3">
              <lucide-icon
                [img]="FolderOpenIcon"
                class="w-5 h-5 text-gray-600 dark:text-gray-400"
              ></lucide-icon>
              <span class="font-medium text-gray-700 dark:text-gray-300">
                Working Directory
              </span>
            </div>
            <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
              <code class="text-sm text-gray-800 dark:text-gray-200">{{
                (appService.configInfo$ | async)?.working_dir
              }}</code>
            </div>
          </div>
          <!-- Action Buttons -->
          @if (validateTabProfileIndex(tab)) {
          <div>
            <div class="flex flex-wrap gap-3">
              <button
                [class]="
                  tab.currentAction === Action.Pull
                    ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                    : 'btn-primary'
                "
                [disabled]="
                  (!validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Pull) ||
                  tab.isStopping
                "
                (click)="
                  tab.currentAction !== Action.Pull
                    ? pullTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <div class="flex items-center space-x-2">
                  <lucide-icon
                    [img]="
                      tab.isStopping
                        ? ClockIcon
                        : tab.currentAction === Action.Pull
                        ? StopCircleIcon
                        : DownloadIcon
                    "
                    class="w-5 h-5 mr-2"
                  ></lucide-icon>
                  {{
                    tab.isStopping
                      ? "Stopping..."
                      : tab.currentAction === Action.Pull
                      ? "Stop Pull"
                      : "Pull"
                  }}
                </div>
              </button>
              <button
                [class]="
                  tab.currentAction === Action.Push
                    ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                    : 'bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                "
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Push
                "
                (click)="
                  tab.currentAction !== Action.Push
                    ? pushTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <div class="flex items-center space-x-2">
                  <lucide-icon
                    [img]="
                      tab.currentAction === Action.Push
                        ? StopCircleIcon
                        : UploadIcon
                    "
                    class="w-5 h-5 mr-2"
                  ></lucide-icon>
                  {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
                </div>
              </button>
              <button
                [class]="
                  tab.currentAction === Action.Bi
                    ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                    : 'btn-primary'
                "
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Bi
                "
                (click)="
                  tab.currentAction !== Action.Bi
                    ? biTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <div class="flex items-center space-x-2">
                  <lucide-icon
                    [img]="
                      tab.currentAction === Action.Bi
                        ? StopCircleIcon
                        : RefreshCwIcon
                    "
                    class="w-5 h-5 mr-2"
                  ></lucide-icon>
                  {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
                </div>
              </button>
              <button
                class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.BiResync
                "
                (click)="
                  tab.currentAction !== Action.BiResync
                    ? biResyncTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <div class="flex items-center space-x-2">
                  <lucide-icon
                    [img]="
                      tab.currentAction === Action.BiResync
                        ? StopCircleIcon
                        : RotateCcwIcon
                    "
                    class="w-5 h-5 mr-2"
                  ></lucide-icon>
                  {{
                    tab.currentAction === Action.BiResync
                      ? "Stop Resync"
                      : "Resync"
                  }}
                </div>
              </button>
            </div>
          </div>
          }
          <!-- Sync Status Display -->
          <div>
            <app-sync-status
              [syncStatus]="tab.syncStatus || null"
              [showTitle]="true"
            >
            </app-sync-status>
          </div>
          <!-- Operation Log Display -->
          @if (tab.data && tab.data.length > 0) {
          <div>
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <lucide-icon
                  [img]="TerminalIcon"
                  class="w-5 h-5 text-gray-600 dark:text-gray-400"
                ></lucide-icon>
                <span class="font-medium text-gray-700 dark:text-gray-300">
                  Operation Log
                </span>
              </div>
              <button
                class="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                (click)="clearTabOutput(tab.id)"
                title="Clear output"
              >
                <lucide-icon [img]="EraseIcon" class="w-4 h-4"></lucide-icon>
              </button>
            </div>
            <div
              class="bg-gray-900 dark:bg-gray-950 rounded-lg p-4 max-h-64 overflow-y-auto"
            >
              <div class="font-mono text-sm text-green-400 whitespace-pre-wrap">
                @for (line of tab.data; track $index) {
                <div>{{ line }}</div>
                }
              </div>
            </div>
          </div>
          }
        </div>
        }
      </div>
      }
    </div>
    <!-- Close padding container div -->
  </div>
  <!-- Close scrollable content div -->
</div>
<!-- Close flex container div -->
}

<!-- Rename Tab Modal -->
@if (showRenameDialog) {
<div
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  (click)="cancelRename()"
  (keyup.escape)="cancelRename()"
  tabindex="0"
  role="dialog"
  aria-modal="true"
  aria-labelledby="rename-dialog-title"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-md mx-4"
    (click)="$event.stopPropagation()"
    (keydown)="$event.stopPropagation()"
    tabindex="-1"
  >
    <h2
      id="rename-dialog-title"
      class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4"
    >
      Rename Tab
    </h2>
    <div class="mb-6">
      <label
        for="rename-tab-input"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        Tab Name
      </label>
      <input
        id="rename-tab-input"
        type="text"
        class="input-field"
        [(ngModel)]="renameDialogData.newName"
        (keydown.enter)="confirmRename()"
        #renameDialogInput
      />
    </div>
    <div class="flex justify-end space-x-3">
      <button class="btn-secondary" (click)="cancelRename()">Cancel</button>
      <button class="btn-primary" (click)="confirmRename()">Rename</button>
    </div>
  </div>
</div>
}
